<template>
  <ActivitiesContainer
    :basicsInfo="props.basicsInfo!"
    :nodeList="baseNode"
    v-model:currentNode="currentNode"
    :stage-count="stageCount"
    title="风险"
  >
    <template #activities-buttons>
      <vxe-toolbar
        size="mini"
        custom
        :export="hasPermission('risk_export')"
        class="w-100%"
        ref="toolbarRef"
      >
        <template #buttons>
          <el-button
            size="small"
            type="primary"
            plain
            @click="
              riskFormRef?.openForm(
                '添加风险：' + baseNode.find((el) => el.id == currentNode).label
              )
            "
            v-if="
              ![1, 4].includes(props.basicsInfo?.status!) &&
              (getVisiableUserList(props.basicsInfo?.id).includes(getUser.id) || getUser.id == 1)
            "
          >
            添加风险
          </el-button>
        </template>
      </vxe-toolbar>
    </template>
    <template #activities-table>
      <RiskTable
        :list="riskList"
        :toolbar="toolbarRef"
        :userList="peopleList"
        :basicsId="props.basicsInfo?.id!"
        @show:form="(row: any) => riskFormRef.openForm('风险详情', row)"
        v-loading="loading"
      />
    </template>
  </ActivitiesContainer>
  <RiskForm
    ref="riskFormRef"
    :basicsId="props.basicsInfo?.id!"
    :stage="currentNode"
    :stageIndex="currentIndex"
    :userList="peopleList"
    @submit:success="onListRisk"
    @open:problem="(row: any) => problemFormRef?.openRisk('添加风险转换问题', row)"
  />
  <ProblemForm
    :template-node="baseNode"
    :basics-id="props.basicsInfo?.id"
    :user-list="peopleList"
    current-node="other"
    ref="problemFormRef"
    @submit:success="riskFormRef?.onFeedback"
  />
</template>

<script lang="ts" setup>
import ActivitiesContainer from '../components/ActivitiesContainer.vue'
import { BasicsVO } from '@/api/project/basics'
import { PropType } from 'vue'
import RiskTable from './RiskTable.vue'
import RiskForm from './RiskForm.vue'
import { ActivitiesTemplateApi } from '@/api/project/activitiestemplate'
import { RiskApi, RiskVO } from '@/api/project/risk'
import ProblemForm from '../problem/ProblemForm.vue'
import { hasPermission, getVisiableUserList } from '../util/permission'
import { useUserStore } from '@/store/modules/user'
import { usePeopleStore } from '@/store/modules/people'

const { getUser } = useUserStore()

const props = defineProps({
  basicsInfo: {
    type: Object as PropType<BasicsVO>,
    default: () => {}
  },
  templateCategory: {
    type: String,
    default: ''
  }
})

const baseNode = ref<any[]>([])
const currentNode = ref<number>(undefined as unknown as number) //当前节点
const riskList = ref<RiskVO[]>([])
const toolbarRef = ref()
const { peopleList } = usePeopleStore()
const riskFormRef = ref()
const problemFormRef = ref()
const loading = ref(false)
const stageCount = ref<any>({})

const getStageCount = async () => {
  const res = await RiskApi.getStageCount(props.basicsInfo!.id!)
  stageCount.value = res
}

/** 获取项目基础节点 */
const onListBaseNode = async () => {
  baseNode.value = []
  const res = await ActivitiesTemplateApi.listActivitiesTemplate({
    parentId: 0,
    categoryId: props.templateCategory
  })
  if (res.length > 0) {
    currentNode.value = res[0].id
  }
  for (const item of res) {
    baseNode.value.push({ id: item.id, label: item.name })
  }
}

// 定义计算属性实时获取位置
const currentIndex = computed(() => {
  return baseNode.value.findIndex((item) => item.id === currentNode.value)
})

const onListRisk = async () => {
  loading.value = true
  try {
    const res = await RiskApi.getRiskList({
      basicsId: props.basicsInfo?.id,
      stage: currentNode.value
    })
    riskList.value = res
  } finally {
    loading.value = false
  }
}

watch(
  () => props.templateCategory,
  () => {
    if (props.templateCategory) {
      onListBaseNode()
    }
  },
  { immediate: true }
)
watch(
  () => [props.basicsInfo, currentNode.value],
  () => {
    if (props.basicsInfo?.id && currentNode.value) {
      getStageCount()
      onListRisk()
    }
  }
)
</script>

<style scoped lang="scss"></style>
