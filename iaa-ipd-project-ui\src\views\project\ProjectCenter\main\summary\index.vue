<template>
  <ActivitiesContainer :basics-info="props.basicsInfo!" :node-list="[]">
    <template #activities-table>
      <div class="h-100% overflow-auto">
        <el-collapse v-model="activeNames" ref="collapseRef" class="custom-collapse">
          <el-collapse-item title="当前阶段重点关注活动" name="1">
            <template #title>
              <div class="flex items-center h-full">
                <CardTitle title="当前阶段重点关注活动" />
                <el-button
                  type="primary"
                  size="small"
                  class="ml-10px"
                  plain
                  @click.stop="
                    focusFormRef?.openForm(
                      props.basicsInfo?.id,
                      focusList?.map((item) => item.id)
                    )
                  "
                  v-getPermi="['summary:add']"
                >
                  设定重点关注任务
                </el-button>
              </div>
            </template>
            <vxe-table
              ref="activitiesTableRef"
              :header-cell-style="{
                padding: '0',
                height: '2.5rem',
                fontSize: '.9rem',
                backgroundColor: '#fafafa',
                color: 'var(--primary-text-color)'
              }"
              :row-style="{
                cursor: 'pointer'
              }"
              :cell-style="{
                padding: '0',
                height: '2.5rem',
                fontSize: '.9rem',
                color: 'var(--primary-text-color)'
              }"
              round
              border
              auto-resize
              :row-config="{ isCurrent: true, isHover: true, userKey: true, keyField: 'id' }"
              :column-config="{ resizable: true, isHover: true }"
              :data="focusList"
              show-overflow
              :loading="loading"
            >
              <vxe-column title="活动主题" field="name" width="14%">
                <template #default="{ row }">
                  <div :style="{ width: `${(row.level - 1) * 10}px`, display: 'inline-block' }">
                  </div>
                  {{ `${row.orderNo}  ${row.name}` }}
                </template>
              </vxe-column>
              <vxe-column title="进度" field="progress" align="center" width="7%">
                <template #default="{ row }">
                  <el-progress
                    :text-inside="true"
                    :stroke-width="22"
                    :percentage="row.progress"
                    status="success"
                    class="no-radius"
                  />
                </template>
              </vxe-column>
              <vxe-column title="状态" field="status" align="center" width="6%">
                <template #default="{ row }">
                  <dict-tag :type="DICT_TYPE.PROJECT_ACTIVITIES_STATUS" :value="row.status" />
                </template>
              </vxe-column>
              <vxe-column title="活动内容" width="15%" field="content" />
              <vxe-column title="活动描述" min-width="24%" field="description" />
              <vxe-column title="时间" align="center" field="date" width="12%">
                <template #default="{ row }">
                  {{ formatToDate(row.startDate, 'YYYY.MM.DD') }}-{{
                    formatToDate(row.endDate, 'YYYY.MM.DD')
                  }}
                </template>
              </vxe-column>
              <vxe-column title="负责人" width="10%" field="director">
                <template #default="{ row }">
                  <user-avatar-list
                    v-model="row.director"
                    :user-list="peopleList"
                    :size="24"
                    :limit="3"
                    :add="false"
                    @click.stop
                  />
                </template>
              </vxe-column>
              <vxe-column title="执行人" width="10%" field="coordinate">
                <template #default="{ row }">
                  <user-avatar-list
                    v-model="row.coordinate"
                    :user-list="peopleList"
                    :size="22"
                    :limit="3"
                    :add="false"
                    @click.stop
                  />
                </template>
              </vxe-column>
            </vxe-table>
          </el-collapse-item>
          <el-collapse-item title="计划总结" name="2">
            <template #title>
              <div class="flex items-center h-full">
                <CardTitle title="计划总结" />
                <el-date-picker
                  v-model="date"
                  type="week"
                  format="YYYY [第] ww [周]"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择周"
                  :clearable="false"
                  @click.stop
                  size="small"
                  class="ml-10px"
                  @change="listSummary"
                />
                <el-button
                  type="primary"
                  size="small"
                  class="ml-10px"
                  plain
                  @click.stop="summaryFormRef?.openForm(date)"
                  v-getPermi="['summary:add']"
                >
                  分解计划总结
                </el-button>
              </div>
            </template>
            <vxe-table
              :header-cell-style="{
                padding: '0',
                height: '2.5rem',
                fontSize: '.9rem',
                backgroundColor: '#fafafa',
                color: 'var(--primary-text-color)'
              }"
              :row-style="{
                cursor: 'pointer'
              }"
              :cell-style="{
                padding: '0',
                height: '2.5rem',
                fontSize: '.9rem',
                color: 'var(--primary-text-color)'
              }"
              round
              border
              auto-resize
              :row-config="{ isCurrent: true, isHover: true, userKey: true, keyField: 'id' }"
              :column-config="{ resizable: true, isHover: true }"
              :data="summaryList"
              show-overflow
              :loading="loading"
              @cell-click="(el) => summaryFormRef?.openForm(date, el.row)"
            >
              <vxe-column title="关注活动" field="活动ID">
                <template #default="{ row }">
                  {{ focusList.find((item) => item.id === row.activitiesId)?.name }}
                </template>
              </vxe-column>
              <vxe-column title="时间" field="date">
                <template #default="{ row }">
                  {{ formatToDate(row.startDate, 'YYYY.MM.DD') }}-{{
                    formatToDate(row.endDate, 'YYYY.MM.DD')
                  }}
                </template>
              </vxe-column>
              <vxe-column title="负责人" field="director">
                <template #default="{ row }">
                  <user-avatar-list
                    v-model="row.director"
                    :user-list="peopleList"
                    :size="24"
                    :limit="3"
                    :add="false"
                    @click.stop
                  />
                </template>
              </vxe-column>
              <vxe-column title="状态" field="status">
                <template #default="{ row }">
                  <dict-tag type="project_summary_status" :value="row.status" />
                </template>
              </vxe-column>
              <vxe-column title="内容" field="description" />
            </vxe-table>
          </el-collapse-item>
        </el-collapse>
      </div>
    </template>
  </ActivitiesContainer>
  <FocusForm ref="focusFormRef" :userList="peopleList" @success="listFocus" />
  <SummaryForm
    ref="summaryFormRef"
    :user-list="peopleList"
    :basics-id="props.basicsInfo?.id"
    :focus-list="focusList"
    @success="listSummary"
  />
</template>

<script lang="ts" setup>
import { ActivitiesVO } from '@/api/project/activities'
import ActivitiesContainer from '../components/ActivitiesContainer.vue'
import { BasicsVO } from '@/api/project/basics'
import { DICT_TYPE } from '@/utils/dict'
import { dateUtil, formatToDate } from '@/utils/dateUtil'
import { SummaryApi, SummaryVO } from '@/api/project/summary'
import FocusForm from './FocusForm.vue'
import SummaryForm from './SummaryForm.vue'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { usePeopleStore } from '@/store/modules/people'
dayjs.locale('zh-cn')

const props = defineProps({
  basicsInfo: {
    type: Object as PropType<BasicsVO>
  }
})
const activeNames = ref(['1', '2'])
const loading = ref(false)
const focusList = ref<ActivitiesVO[]>([])
const { peopleList } = usePeopleStore()
const date = ref(dateUtil().format('YYYY-MM-DD'))
const focusFormRef = ref()
const summaryFormRef = ref()
const summaryList = ref<SummaryVO[]>([])

const listFocus = async () => {
  loading.value = true
  try {
    const res = await SummaryApi.getFocusActivities(props.basicsInfo!.id!)
    focusList.value = res
  } finally {
    loading.value = false
  }
}

const listSummary = async () => {
  loading.value = true
  try {
    const res = await SummaryApi.getSummaryItemList({
      basicsId: props.basicsInfo!.id!,
      startDate: dayjs(date.value).startOf('week').format('YYYY-MM-DD'),
      endDate: dayjs(date.value).endOf('week').format('YYYY-MM-DD')
    })
    summaryList.value = res
  } finally {
    loading.value = false
  }
}

watch(
  () => props.basicsInfo?.id,
  () => {
    if (props.basicsInfo?.id) {
      listFocus()
      listSummary()
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
:deep(.el-card__header),
:deep(.el-card__body) {
  padding: 10px;
}

:deep(.el-collapse-item__header) {
  background-color: #fff !important;
  border: 0.3px solid var(--el-color-info-light-7) !important;
  border-left: none;
  font-size: 1rem;
  height: 40px;
  padding-left: 10px;
}
:deep(.el-collapse-item__content) {
  border: 0.3px solid var(--el-color-info-light-7) !important;
  border-top: none !important;
  border-bottom: none !important;
  background-color: #fafafa;
}
</style>
