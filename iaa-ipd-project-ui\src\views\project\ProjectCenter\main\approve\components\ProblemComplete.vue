<template>
  <el-tabs
    v-model="currentTab"
    class="drawer-tabs position-sticky top-0 z-10 bg-#fff"
    @tab-change="onTabChange"
  >
    <el-tab-pane name="problem" label="问题" />
    <el-tab-pane name="target" label="输出物" />
  </el-tabs>
  <el-form label-width="100px" ref="formRef" class="custom-form" v-if="currentTab == 'problem'">
    <el-form-item label="所属项目">
      <el-input v-model="formData.basicsName" :disabled="true" />
    </el-form-item>
    <el-row>
      <el-col :span="8">
        <el-form-item label="等级">
          <el-input v-model="formData.basicsLevel" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="类型">
          <el-input v-model="formData.basicsMold" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="平台">
          <el-input v-model="formData.basicsPlatform" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item label="描述" prop="content">
      <el-input type="textarea" :rows="4" v-model="formData.content" :disabled="true" />
    </el-form-item>
    <el-form-item label="图片" prop="imgIds">
      <UploadImgs v-model="formData.imgIds!" height="60px" width="60px" :disabled="true" />
    </el-form-item>

    <el-row>
      <el-col :span="8">
        <el-form-item label="等级" prop="level">
          <el-select v-model="formData.level" :disabled="true">
            <el-option
              v-for="dict in getStrDictOptions('project_problem_level')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="分类" prop="category">
          <el-select v-model="formData.category" :disabled="true">
            <el-option
              v-for="dict in getStrDictOptions('project_problem_category')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="责任模块" prop="module">
          <el-select v-model="formData.module" :disabled="true">
            <el-option
              v-for="dict in getStrDictOptions('project_problem_module')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="8">
        <el-form-item label="提出部门" prop="proposingDepartment">
          <el-select v-model="formData.proposingDepartment" :disabled="true">
            <el-option
              v-for="dict in getStrDictOptions('project_problem_proposing_department')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="阶段" prop="stage">
          <el-select v-model="formData.stage" :disabled="true">
            <el-option
              v-for="item in templateNode"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="不良比例(%)" prop="rejectRatio">
          <el-input-number
            v-model="formData.rejectRatio"
            :min="1"
            :max="100"
            :disabled="true"
            class="!w-full"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="8">
        <el-form-item label="状态">
          <DictTag type="project_activities_status" :value="formData.status!" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="提出时间" prop="timeOfProposal">
          {{ formatDate(formData.timeOfProposal, 'YYYY-MM-DD') }}
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="计划完成" prop="timeOfPlan">
          {{ formatDate(formData.timeOfPlan, 'YYYY-MM-DD') }}
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item label="责任人" prop="director">
      <user-avatar-list
        v-model="formData.director!"
        :user-list="peopleList"
        :size="28"
        :limit="10"
        :add="false"
      />
    </el-form-item>
    <el-form-item label="执行人">
      <user-avatar-list
        v-model="formData.coordinate!"
        :user-list="peopleList"
        :size="28"
        :limit="10"
        :add="false"
      />
    </el-form-item>
    <el-form-item label="进度" v-if="formData.id">
      <el-progress
        :percentage="formData.progress"
        class="w-100% no-radius"
        :text-inside="true"
        :stroke-width="20"
        status="success"
      />
    </el-form-item>
    <el-form-item label="原因分析" prop="reason">
      <el-input type="textarea" :rows="4" v-model="formData.reason" :disabled="true" />
    </el-form-item>

    <el-form-item label="解决措施" prop="measures">
      <el-input type="textarea" :rows="4" v-model="formData.measures" :disabled="true" />
    </el-form-item>
    <Comment
      ref="commentRef"
      category="problem"
      :limit="5"
      bgColor="#fff"
      :disabled="false"
      :user-list="[]"
    />
  </el-form>
  <template v-else-if="currentTab === 'target'">
    <vxe-table
      class="w-100%"
      :header-cell-style="{ padding: '5px', backgroundColor: '#fff' }"
      :cell-style="{ padding: '5px', height: '30px' }"
      show-overflow
      :data="attachmentList"
      align="center"
      border
    >
      <vxe-column title="文件名" field="name" min-width="200" align="left">
        <template #default="{ row }">
          <el-link
            type="primary"
            @click="attachmentPreviewRef?.openForm(row.name, row.processInstanceId)"
          >
            {{ row.name }}
          </el-link>
        </template>
      </vxe-column>
      <vxe-column title="版本" field="currentVersion" width="60" />
      <vxe-column title="审签状态" field="approvalStatus" width="90">
        <template #default="{ row }">
          <DictTag type="project_target_approval_status" :value="row.approvalStatus" />
        </template>
      </vxe-column>
      <vxe-column
        title="审核通过时间"
        field="approvalTime"
        :formatter="dateFormatter3"
        width="120"
      />
    </vxe-table>
  </template>
  <AttachmentPreview ref="attachmentPreviewRef" />
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { getStrDictOptions } from '@/utils/dict'
import { ProblemFlowApi } from '@/api/bpm/problem'
import { ActivitiesTemplateApi } from '@/api/project/activitiestemplate'
import { dateFormatter3, formatDate } from '@/utils/formatTime'
import { AttachmentApi, AttachmentRespVO } from '@/api/project/attachment'
import AttachmentPreview from '../../components/AttachmentPreview.vue'
import { usePeopleStore } from '@/store/modules/people'

const props = defineProps({
  processInstanceId: propTypes.string.def('')
})

interface Node {
  id: number | string
  label: string
}
const currentTab = ref('problem')
const templateNode = ref<Node[]>([])
const formData = ref<any>({})
const { peopleList } = usePeopleStore()
const attachmentList = ref<AttachmentRespVO[]>([])
const attachmentPreviewRef = ref()

const commentRef = ref()

const onListComment = async () => {
  commentRef.value?.listEvent(formData.value.problemId)
}

const init = async () => {
  const res = await ProblemFlowApi.getProblem(props.processInstanceId)
  formData.value = res
  templateNode.value = []
  const res1 = await ActivitiesTemplateApi.listActivitiesTemplate({
    parentId: 0,
    categoryId: res.templateCategory
  })
  for (const item of res1) {
    templateNode.value.push({ id: item.id, label: item.name })
  }
  onListComment()
}

/** 获取附件列表 */
const onListAttachment = async () => {
  const res = await AttachmentApi.getAttachmentList({
    category: 'problem',
    dynamicId: formData.value.problemId
  })
  attachmentList.value = res
}

watch(
  () => props.processInstanceId,
  () => {
    if (props.processInstanceId) {
      init()
    }
  },
  { immediate: true }
)

const onTabChange = async () => {
  switch (currentTab.value) {
    case 'target':
      onListAttachment()
      break
  }
}
</script>
