<template>
  <content-wrap>
    <el-form inline class="custom-form" @submit.prevent>
      <el-form-item label="规格书名称">
        <el-input v-model="queryParams.name" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleList">查询</el-button>
        <el-popover
          trigger="click"
          placement="bottom-start"
          width="300"
          v-hasPermi="['specificaton:doc:create']"
        >
          <template #reference>
            <el-button>
              创建
              <Icon icon="ep:arrow-down" />
            </el-button>
          </template>
          <el-input v-model="templateName">
            <template #append>
              <el-button :icon="Search" @click="onTemplateList" />
            </template>
          </el-input>
          <div class="max-h-200px w-full border-1px border-solid border-#f1f1f1 rounded mt-5px">
            <div
              class="flex p-5px justify-between border-b-1px border-b-solid border-b-#f1f1f1"
              v-for="(item, index) in templateList"
              :key="index"
            >
              <el-button type="primary" link @click="openSpecificationDrawer(item, false)">
                {{ item.name }}
              </el-button>
              <el-button link type="warning" @click="openPreviewForm(item.content)">预览</el-button>
            </div>
          </div>
        </el-popover>
      </el-form-item>
    </el-form>
  </content-wrap>
  <content-wrap>
    <div class="h-[calc(100vh-250px)]">
      <vxe-table
        ref="tableRef"
        height="100%"
        :data="list"
        :loading="loading"
        :header-cell-config="{ height: 30 }"
        :cell-config="{ height: 30 }"
        :cell-style="{ cursor: 'pointer' }"
        border
        stripe
        align="center"
        show-overflow
        :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }"
        @cell-click="
          (el) => {
            if (el.column.field !== 'name' || !checkPermi(['specificaton:doc:create'])) {
              openSpecificationDrawer({}, false, el.row)
            }
          }
        "
        @edit-closed="handleEditClosed"
        size="mini"
      >
        <vxe-column
          title="名称"
          field="name"
          :edit-render="{ name: 'input', enabled: checkPermi(['specificaton:doc:create']) }"
        >
          <template #edit="scope">
            <vxe-input
              type="text"
              v-model="scope.row.name"
              @input="updateRowStatus(scope)"
              :disabled="scope.row.creatorId != getUser.id"
            />
          </template>
        </vxe-column>
        <vxe-column title="模板" field="templateName" />
        <vxe-column title="物料列表" field="materials" />
        <vxe-column title="当前版本" field="version" width="80">
          <template #default="{ row }">
            <el-button type="primary" link @click.stop="onListHistory(row.code)">
              {{ `V${row.version}` }}
            </el-button>
          </template>
        </vxe-column>
        <vxe-column title="状态" field="status" width="170">
          <template #default="{ row }">
            <DictTag type="specification_status" :value="row.status" />
            <el-button
              type="primary"
              link
              v-if="[1, 2, 3].includes(row.status)"
              @click.stop="toBpm(row.processInstanceId)"
            >
              跳转流程
            </el-button>
          </template>
        </vxe-column>
        <vxe-column title="创建人" field="creator" width="80" />
        <vxe-column title="创建时间" field="createTime" :formatter="dateFormatter3" width="160" />
        <vxe-column title="操作" field="operation" width="140">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              v-hasPermi="['specificaton:doc:create']"
              @click.stop="
                openSpecificationDrawer({}, false, {
                  id: undefined,
                  code: undefined,
                  name: undefined,
                  templateId: row.templateId,
                  templateName: row.templateName,
                  templateCode: row.templateCode,
                  templateContent: row.templateContent,
                  templateIsLatest: row.templateIsLatest,
                  version: 1,
                  data: row.data,
                  status: 0
                })
              "
            >
              复制
            </el-button>
            <el-button
              type="warning"
              link
              size="small"
              v-hasPermi="['specificaton:doc:create']"
              @click.stop="openRelatedMaterialsDialog(row)"
            >
              关联物料
            </el-button>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <div class="h-50px">
      <Pagination
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        :total="total"
        size="small"
        @pagination="onList"
      />
    </div>
    <Dialog title="历史版本" v-model="historyVisible">
      <vxe-table
        :data="historyList"
        :loading="loading"
        :header-cell-config="{ height: 44 }"
        :cell-config="{ height: 44 }"
        :cell-style="{ cursor: 'pointer' }"
        border
        stripe
        align="center"
        show-overflow
      >
        <vxe-column title="名称" field="name">
          <template #default="{ row }">
            <el-button type="primary" link @click="specificationRef?.openForm({}, true, row)">
              {{ row.name }}
            </el-button>
          </template>
        </vxe-column>
        <vxe-column title="模板" field="templateName" />
        <vxe-column title="版本" field="version">
          <template #default="{ row }">
            {{ `V${row.version}` }}
          </template>
        </vxe-column>
        <vxe-column title="创建人" field="creator" />
        <vxe-column title="创建时间" field="createTime" :formatter="dateFormatter3" />
      </vxe-table>
    </Dialog>

    <Dialog title="规格书关联物料" v-model="relatedMaterialsForm.visible">
      <el-form label-width="120">
        <el-form-item label="规格书">
          {{ relatedMaterialsForm.name }}
        </el-form-item>
        <el-form-item label="选择物料">
          <el-select-v2
            v-model="relatedMaterialsForm.materials"
            multiple
            filterable
            :options="relatedMaterialsForm.materialList"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="onSaveRelatedMaterials">保存</el-button>
      </template>
    </Dialog>
  </content-wrap>

  <NoModalDrawer title="预览表单" v-model="previewFormVisible" size="60%">
    <AmisRenderer :formid="previewForm?.id" :formjson="previewForm" />
  </NoModalDrawer>

  <NoModalDrawer v-model="specificationDrawerVisible" size="60%">
    <template #header>
      <div class="flex justify-between items-center">
        <div class="text-white font-bold text-17px">
          {{ rowData?.name || '创建' + template?.name }}
        </div>
        <el-button link v-if="rowData?.id" @click="specificationRef?.onExport()">
          <Icon icon="ep:download" />
          <span class="text-white">下载为word文件</span>
        </el-button>
      </div>
    </template>
    <SpecificationForm
      ref="specificationRef"
      @success="onList"
      @cancel="specificationDrawerVisible = false"
    />
  </NoModalDrawer>
</template>

<script setup lang="ts">
import { SpecificationApi } from '@/api/project/specification'
import { dateFormatter3 } from '@/utils/formatTime'
import SpecificationForm from './SpecificationForm.vue'
import { Search } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/modules/user'
import { DrawingUploadApi } from '@/api/project/drawingupload'
import { checkPermi } from '@/utils/permission'

const queryParams = reactive({
  pageNo: 1,
  pageSize: 30,
  name: undefined
})
const { getUser } = useUserStore()
const message = useMessage()
const total = ref(0)
const list = ref<any[]>([])
const loading = ref(false)
const specificationRef = ref()

const templateName = ref('')
const templateList = ref<any[]>([])

const historyVisible = ref(false)
const historyList = ref<any[]>([])

const previewFormVisible = ref(false)
const previewForm = ref<any>({})

const specificationDrawerVisible = ref(false)
const template = ref<any>({})
const rowData = ref<any>({})
const router = useRouter()

const relatedMaterialsForm = reactive({
  visible: false,
  id: undefined,
  name: undefined,
  materials: undefined,
  materialList: []
})

const openRelatedMaterialsDialog = async (row: any) => {
  relatedMaterialsForm.visible = true
  relatedMaterialsForm.id = row.id
  relatedMaterialsForm.name = row.name
  relatedMaterialsForm.materials = row.materials
}

const onListMaterial = async () => {
  const res = await DrawingUploadApi.getDrawingUploadList()
  relatedMaterialsForm.materialList = res.map((item) => {
    return {
      value: item.materialCode,
      label: item.materialCode + ' - ' + item.materialName
    }
  })
}

const onSaveRelatedMaterials = async () => {
  await message.confirm('确认保存关联的物料？')
  await SpecificationApi.updateSpecificationName({
    id: relatedMaterialsForm.id,
    materials: relatedMaterialsForm.materials
  })
  message.success('保存成功')
  relatedMaterialsForm.visible = false
  relatedMaterialsForm.id = undefined
  relatedMaterialsForm.name = undefined
  relatedMaterialsForm.materials = undefined
  onList()
}

const openSpecificationDrawer = async (tempTemplate: any, history: boolean, row?: any) => {
  loading.value = true
  try {
    specificationDrawerVisible.value = true
    template.value = tempTemplate
    await nextTick()
    rowData.value = row
    specificationRef.value?.openForm(tempTemplate, history, row)
  } finally {
    loading.value = false
  }
}

const handleEditClosed = (params: any) => {
  // console.log(params, 'params')
  const { row, column, editStore } = params

  // 现在可以正确获取到值了
  // console.log('旧值:', column.model.value);
  // console.log('新值:', row.name);
  // 只处理名称列的编辑
  if (column.field === 'name') {
    // 只有当值确实发生变化时才调用接口
    if (column.model.value !== row.name) {
      SpecificationApi.updateSpecificationName({ id: row.id, name: row.name })
      message.success('修改成功!')
    }
  }
}

const openPreviewForm = (content: string) => {
  previewFormVisible.value = true
  previewForm.value = JSON.parse(content)
}
const tableRef = ref()
const updateRowStatus = (params: any) => {
  const $table = tableRef.value
  if ($table) {
    return $table.updateStatus(params)
  }
}
const onList = async () => {
  loading.value = true
  try {
    const res = await SpecificationApi.pageSpecification(queryParams)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const onListHistory = async (code: string) => {
  historyList.value = await SpecificationApi.listHistoryByCode(code)
  historyVisible.value = true
}

const handleList = () => {
  queryParams.pageNo = 1
  onList()
}

const onTemplateList = async () => {
  const res = await SpecificationApi.listDocTemplate(templateName.value)
  templateList.value = res
}

const toBpm = (id: string) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: id
    }
  })
}

onMounted(() => {
  onList()
  onTemplateList()
  onListMaterial()
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px !important;
}
</style>
