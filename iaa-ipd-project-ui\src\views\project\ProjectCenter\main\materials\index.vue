<template>
  <ActivitiesContainer
    :basics-info="props.basicsInfo"
    :node-list="[
      { label: '流程管控', id: 'all-flow' },
      { label: '看板报表', id: 'report' }
    ]"
    v-model:current-node="currentNode"
    :show-total="false"
  >
    <template #activities-table>
      <template v-if="props.basicsInfo?.erpCode">
        <vxe-split class="h-[calc(100%-20px)]" v-if="currentNode == 'all-flow'">
          <vxe-split-pane>
            <div class="p-10px h-full">
              <div class="crux-grid">
                <div class="crux-card">
                  <div class="crux-icon">
                    <Icon icon="mdi:hammer-wrench" />
                  </div>
                  <div class="crux-content">
                    <div class="crux-title">小试</div>
                    <div class="crux-value" v-if="cruxNodeList.get('xiaoshi')">
                      <div>计划：{{ cruxNodeList.get('xiaoshi')?.endDate }}</div>
                      <div>实际：{{ cruxNodeList.get('xiaoshi')?.completedDate }}</div>
                    </div>
                    <div class="crux-value" v-else>暂无数据</div>
                  </div>
                </div>
                <div class="crux-card">
                  <div class="crux-icon prototype">
                    <Icon icon="mdi:cube-outline" />
                  </div>
                  <div class="crux-content">
                    <div class="crux-title">物料承认</div>
                    <div class="crux-value" v-if="cruxNodeList.get('sample')">
                      <div>计划：{{ cruxNodeList.get('sample')?.endDate }}</div>
                      <div>实际：{{ cruxNodeList.get('sample')?.completedDate }}</div>
                    </div>
                    <div class="crux-value" v-else>暂无数据</div>
                  </div>
                </div>
                <div class="crux-card">
                  <div class="crux-icon handboard">
                    <Icon icon="mdi:hand-saw" />
                  </div>
                  <div class="crux-content">
                    <div class="crux-title">签样</div>
                    <div class="crux-value" v-if="cruxNodeList.get('approval_sample')">
                      <div>计划：{{ cruxNodeList.get('approval_sample')?.endDate }}</div>
                      <div>实际：{{ cruxNodeList.get('approval_sample')?.completedDate }}</div>
                    </div>
                    <div class="crux-value" v-else>暂无数据</div>
                  </div>
                </div>
                <div class="crux-card">
                  <div class="crux-icon certification">
                    <Icon icon="mdi:certificate-outline" />
                  </div>
                  <div class="crux-content">
                    <div class="crux-title">中试</div>
                    <div class="crux-value" v-if="cruxNodeList.get('zhongshi')">
                      <div>计划：{{ cruxNodeList.get('zhongshi')?.endDate }}</div>
                      <div>实际：{{ cruxNodeList.get('zhongshi')?.completedDate }}</div>
                    </div>
                    <div class="crux-value" v-else>暂无数据</div>
                  </div>
                </div>
              </div>
              <div class="h-[calc(100%-100px)]">
                <vxe-table
                  ref="flowTableRef"
                  :data="materialList"
                  height="100%"
                  align="center"
                  border
                  show-overflow
                  :header-cell-config="{ height: 30 }"
                  :column-config="{ resizable: true, isHover: true }"
                  :cell-config="{ height: 36 }"
                  :row-config="{ isHover: true, isCurrent: true }"
                  :row-style="{ cursor: 'pointer' }"
                  @cell-click="(el) => (currentRow = el.row)"
                  :loading="loading"
                  :edit-config="{ trigger: 'click', model: 'cell' }"
                  @edit-closed="onEditClosed"
                >
                  <vxe-column title="品号" field="code" width="100" />
                  <vxe-column title="品名" field="name" width="200" />
                  <vxe-column title="规格" field="spec" min-width="200" align="left" />
                  <vxe-column title="小试负责人" field="xiaoshiDirector" width="150">
                    <template #default="{ row }">
                      <user-avatar-list
                        v-model="row.xiaoshiDirector"
                        :user-list="peopleList"
                        :size="24"
                        :limit="3"
                        :add="hasPermission('activities_disassemble', props.basicsInfo?.id)"
                        :visiable-user-list="[...getVisiableUserList(), ...supportlibraryList]"
                        @click.stop
                        @change:msg="onDirectorChange('xiaoshi', row)"
                      />
                    </template>
                  </vxe-column>
                  <vxe-column title="物料承认负责人" field="simpleDirector" width="150">
                    <template #default="{ row }">
                      <user-avatar-list
                        v-model="row.simpleDirector"
                        :user-list="peopleList"
                        :size="24"
                        :limit="3"
                        :add="hasPermission('activities_disassemble', props.basicsInfo?.id)"
                        :visiable-user-list="[...getVisiableUserList(), ...supportlibraryList]"
                        @click.stop
                        @change:msg="onDirectorChange('simple', row)"
                      />
                    </template>
                  </vxe-column>
                  <vxe-column title="签样负责人" field="approvalSampleDirector" width="150">
                    <template #default="{ row }">
                      <user-avatar-list
                        v-model="row.approvalSampleDirector"
                        :user-list="peopleList"
                        :size="24"
                        :limit="3"
                        :add="hasPermission('activities_disassemble', props.basicsInfo?.id)"
                        :visiable-user-list="[...getVisiableUserList(), ...supportlibraryList]"
                        @click.stop
                        @change:msg="onDirectorChange('approvalSample', row)"
                      />
                    </template>
                  </vxe-column>
                  <vxe-column
                    title="签样图片"
                    field="approvalSampleImgs"
                    width="80"
                    :cell-render="imgUrlCellRender"
                  />
                  <vxe-column title="签样状态" field="approvalSampleStatus" width="80">
                    <template #default="{ row }">
                      <DictTag
                        type="material_approval_sample_status"
                        :value="row.approvalSampleStatus"
                      />
                    </template>
                  </vxe-column>
                  <vxe-column
                    title="物料下发时间"
                    field="distributionTime"
                    :edit-render="{
                      name: 'VxeDatePicker',
                      enabled: hasPermission('activities_disassemble', props.basicsInfo?.id)
                    }"
                    width="140"
                  />
                </vxe-table>
              </div>
            </div>
          </vxe-split-pane>
          <vxe-split-pane width="300px" min-width="300px">
            <div class="p-10px h-full" v-if="currentRow">
              <div class="p-5px !text-1rem font-bold"> 当前选择：{{ currentRow?.code }} </div>
              <el-timeline class="!p-0px">
                <el-timeline-item color="#ffee6f">
                  <el-card shadow="never" body-class="!p-10px" class="xiaoshi">
                    <template #header> 小试图纸 </template>
                    <MaterialAttachment
                      :material-id="currentRow?.id"
                      type="10"
                      :permission="currentRow?.xiaoshiDirector"
                    />
                  </el-card>
                </el-timeline-item>
                <el-timeline-item color="#b1d5c8">
                  <el-card shadow="never" body-class="!p-10px" class="sample">
                    <template #header> 物料承认书 </template>
                    <MaterialAttachment
                      :material-id="currentRow?.id"
                      type="20"
                      :permission="currentRow?.simpleDirector"
                    />
                  </el-card>
                </el-timeline-item>
                <el-timeline-item color="#ee7959">
                  <el-card shadow="never" body-class="!p-10px" class="approvel-sample">
                    <template #header>
                      <div class="flex justify-between">
                        <div>签样</div>
                        <el-button
                          type="primary"
                          link
                          v-if="currentRow?.processInstanceId"
                          @click="toBpm(currentRow?.processInstanceId)"
                        >
                          查看流程
                        </el-button>
                      </div>
                    </template>
                    <UploadImg
                      width="100%"
                      height="80px"
                      v-model="currentRow.approvalSampleImgs"
                      :disabled="currentRow?.approvalSampleStatus"
                    />
                    <el-button
                      class="!w-100% !border-dashed"
                      size="small"
                      v-if="[0, 3].includes(currentRow?.approvalSampleStatus)"
                      plain
                      @click="onApprovalSample"
                    >
                      发起签样审批
                    </el-button>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </div>
            <el-empty v-else description="请选择左侧物料行查看" />
          </vxe-split-pane>
        </vxe-split>
        <div class="h-[calc(100%-20px)]" v-else>
          <vxe-toolbar ref="toolbarRef" size="mini" custom export />
          <div class="h-[calc(100%-50px)]">
            <vxe-table
              ref="tableRef"
              :data="materialKanbanList"
              height="100%"
              align="center"
              border
              show-overflow
              :header-cell-config="{ height: 30 }"
              :column-config="{ resizable: true, isHover: true }"
              :cell-config="{ height: 36 }"
              :row-config="{ isHover: true, isCurrent: true }"
              :loading="loading"
              :export-config="{ remote: true, message: false, exportMethod: onExport }"
            >
              <vxe-column title="品号" field="code" width="90" />
              <vxe-column title="品名" field="name" width="100" />
              <vxe-column title="规格" field="spec" min-width="150" />
              <vxe-column title="小试图纸" field="xiaoshiFileName" width="100">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    link
                    @click="officeEditorRef?.open(row?.xiaoshiFileId, row?.xiaoshiFileName)"
                  >
                    {{ row.xiaoshiFileName }}
                  </el-button>
                </template>
              </vxe-column>
              <vxe-column title="小试计划时间" field="xiaoshiPlanDate" width="120" />
              <vxe-column title="小试完成时间" field="xiaoshiDate" width="120" />
              <vxe-column title="小试外发数量" field="xiaoshiSendQty" width="110" />
              <vxe-column title="小试采购数量" field="xiaoshiPurchaseQty" width="110" />
              <vxe-column title="小试收货数量" field="xiaoshiReceiveQty" width="110" />
              <vxe-column title="物料承认文件" field="sampleFileName" width="120">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    link
                    @click="officeEditorRef?.open(row?.sampleFileId, row?.sampleFileName)"
                  >
                    {{ row.sampleFileName }}
                  </el-button>
                </template>
              </vxe-column>
              <vxe-column title="物料承认计划时间" field="samplePlanDate" width="140" />
              <vxe-column title="物料承认时间" field="sampleDate" width="120" />
              <vxe-column
                title="签样图片"
                field="approvalSampleImgs"
                :cell-render="imgUrlCellRender"
                width="80"
              />
              <vxe-column title="签样时间" field="approvalSampleTime" width="140">
                <template #default="{ row }">
                  {{ row?.approvalSampleTime ? formatToDateTime(row.approvalSampleTime) : '' }}
                </template>
              </vxe-column>
              <vxe-column title="中试计划时间" field="zhongshiPlanDate" width="110" />
              <vxe-column title="中试完成时间" field="zhongshiDate" width="110" />
              <vxe-column title="中试外发数量" field="zhongshiSendQty" width="110" />
              <vxe-column title="中试采购数量" field="zhongshiPurchaseQty" width="110" />
              <vxe-column title="中试收货数量" field="zhongshiReceiveQty" width="110" />
              <vxe-column title="物料下发时间" field="distributionTime" width="110" />
            </vxe-table>
          </div>
        </div>
      </template>
      <el-empty v-else description="当前项目未绑定ERP编码" />
      <OfficeEditor ref="officeEditorRef" has-dialog :download="true" />
    </template>
  </ActivitiesContainer>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { MaterialApi } from '@/api/project/material'
import ActivitiesContainer from '../components/ActivitiesContainer.vue'
import MaterialAttachment from './MaterialAttachment.vue'
import UploadImg from '@/components/UploadFile/src/UploadImg.vue'
import { hasPermission, getVisiableUserList } from '../util/permission'
import { SupportLibraryApi } from '@/api/project/supportlibrary'
import type { VxeColumnPropTypes } from 'vxe-table'
import { formatToDateTime } from '@/utils/dateUtil'
import download from '@/utils/download'
import { useCache } from '@/hooks/web/useCache'
import { usePeopleStore } from '@/store/modules/people'

const supportlibraryList = ref<number[]>([])
const getSupportLibraryList = async () => {
  supportlibraryList.value = await SupportLibraryApi.getSimpleList()
}

const props = defineProps({
  basicsInfo: propTypes.any.isRequired
})
const currentNode = ref('all-flow')
const cruxNodeList = ref(new Map<string, any>())
const materialList = ref<any[]>([])
const materialKanbanList = ref<any[]>([])
const currentRow = ref<any>(undefined)
const loading = ref(false)
const message = useMessage()
const { peopleList } = usePeopleStore()
const router = useRouter()
const officeEditorRef = ref()
const toolbarRef = ref()
const tableRef = ref()
const flowTableRef = ref()

const onExport = async ({ options }: any) => {
  try {
    if (!props.basicsInfo?.id) return
    // 导出的二次确认
    await message.exportConfirm()
    // const columns = options.columns.map((item) => item.field)
    // if (columns.length === 0) {
    //   message.warning('未选择需要导出的列')
    //   return
    // }
    // if (columns.includes('date')) {
    //   columns.splice(columns.indexOf('date'), 1, 'startDate', 'endDate')
    // }
    const data = await MaterialApi.exportKanbanList(props.basicsInfo?.id)
    download.excel(data, `${options.filename}.xlsx`)
  } catch {}
}

/** 跳转流程详情 */
const toBpm = (id: string) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: id
    }
  })
}

const onEditClosed = async ({ row }) => {
  await MaterialApi.saveMaterial(row)
  message.success('保存成功')
}

/** 获取关键节点数据 */
const onListCruxNode = async () => {
  const res = await MaterialApi.getMaterialCruxNode(props.basicsInfo?.id)
  const map = new Map<string, any>()
  res.forEach((item) => {
    if (item.setting) {
      // 假设 setting 是字符串，包含多个节点标识，用逗号分隔
      const settings = item.setting
      settings.forEach((setting: string) => {
        map.set(setting, item)
      })
    }
  })
  cruxNodeList.value = map
}
/** 获取物料信息 */
const onListMaterial = async () => {
  loading.value = true
  try {
    if (!(props.basicsInfo?.id && props.basicsInfo?.erpCode)) return
    // await MaterialApi.syncMaterial(props.basicsInfo?.id, props.basicsInfo?.erpCode)
    materialList.value = await MaterialApi.getMaterialList(props.basicsInfo?.id)
    currentRow.value = materialList.value.find((item) => item.code == currentRow.value?.code)
    const form = wsCache.get('project_page_show_form')
    if (form && form?.id) {
      await nextTick()
      const data = unref(flowTableRef)?.getTableData()
      const row = data?.tableData?.find((item) => (item.itemId = form?.id))
      unref(flowTableRef)?.setCurrentRow(row)
      currentRow.value = row
      wsCache.delete('project_page_show_form')
    }
  } finally {
    loading.value = false
  }
}
const { wsCache } = useCache()
const onListMaterialKanban = async () => {
  loading.value = true
  try {
    if (!props.basicsInfo?.id) return
    materialKanbanList.value = await MaterialApi.getKanbanList(props.basicsInfo.id)
  } finally {
    loading.value = false
  }
}

const onApprovalSample = async () => {
  if (!(currentRow.value?.approvalSampleImgs && currentRow.value?.approvalSampleImgs.length > 0)) {
    message.alertError('未上传签样图片，无法签样')
    return
  }
  await MaterialApi.createMaterialSample(currentRow.value)
  onListMaterial()
  message.success('成功')
}

const onDirectorChange = async (type: string, row: any) => {
  await MaterialApi.saveDirector({
    id: row.id,
    type,
    director: row[`${type}Director`]
  })
  message.success('设定成功')
  onListMaterial()
}

const imgUrlCellRender = reactive<VxeColumnPropTypes.CellRender>({
  name: 'VxeImage',
  props: {
    width: 36,
    height: 36
  }
})

watch(
  () => props.basicsInfo?.id,
  () => {
    currentRow.value = undefined
    if (!props.basicsInfo?.id) return
    onListCruxNode()
    onListMaterial()
    onListMaterialKanban()
  },
  { immediate: true }
)

watch(
  () => currentNode.value,
  (newVal) => {
    currentRow.value = undefined
    if (newVal === 'all-flow') {
      onListCruxNode()
      onListMaterial()
    } else {
      onListMaterialKanban()
      nextTick(() => {
        unref(tableRef)?.connect(unref(toolbarRef))
      })
    }
  }
)
onMounted(() => {
  getSupportLibraryList()
})
</script>

<style lang="scss" scoped>
/* 费用卡片网格 */
.crux-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  height: 80px;
  margin-bottom: 20px;
}

.crux-card {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    border-color: #cbd5e1;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}
.crux-setting {
  position: absolute;
  right: 5px;
  top: 5px;
  cursor: pointer;
}

.crux-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #ffffff;
  background: #3b82f6;

  &.prototype {
    background: #10b981;
  }

  &.handboard {
    background: #f59e0b;
  }

  &.certification {
    background: #8b5cf6;
  }

  &.factory-price {
    background: #ef4444;
  }
}

.crux-content {
  flex: 1;
  min-width: 0;
}

.crux-title {
  font-size: 0.7rem;
  color: #64748b;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.crux-value {
  font-size: 0.8rem;
  font-weight: 500;
  color: #1e293b;
}

:deep(.el-card__header) {
  padding: 10px !important;
  font-size: 0.8rem;
}
:deep(.xiaoshi .el-card__header) {
  background-color: #3b82f6;
  color: #fff;
  font-weight: bold;
}
:deep(.sample .el-card__header) {
  background-color: #10b981;
  color: #fff;
  font-weight: bold;
}
:deep(.approvel-sample .el-card__header) {
  background-color: #f59e0b;
  color: #fff;
  font-weight: bold;
}
</style>
