<template>
  <ActivitiesContainer :basicsInfo="props.basicsInfo!" :nodeList="[]">
    <template #activities-table>
      <div class="flex h-full w-full p-t-10px">
        <div class="tr-container">
          <div
            :class="['tr-item', currentNode === dict.value ? 'active' : '']"
            v-for="dict in getIntDictOptions('project_decision_category')"
            :key="dict.value"
            @click="onNodeChange(dict.value)"
          >
            <div class="tr-item-title">{{ dict.label }}</div>
          </div>
        </div>
        <div class="w-87% h-97% bg-#ebeff3 overflow-auto" v-loading="loading">
          <template v-if="currentNode">
            <!-- <div class="header text-center">
              <el-radio-group size="small" v-model="currentPage" @change="onTabChange">
                <el-radio-button label="问题列表" value="problem" />
                
              </el-radio-group>
            </div> -->
            <!-- <el-radio-button label="评审报告" value="record" /> -->
            <template v-if="currentPage === 'problem'">
              <DecisionTable
                :userList="peopleList"
                :decisionList="decisionList"
                :category="currentNode"
                :basicsId="props.basicsInfo!.id"
                @submit:success="onListDecision"
              />
            </template>
          </template>
        </div>
      </div>
    </template>
  </ActivitiesContainer>
</template>

<script lang="ts" setup>
import { BasicsVO } from '@/api/project/basics'
import ActivitiesContainer from '../components/ActivitiesContainer.vue'
import { getIntDictOptions } from '@/utils/dict'
import { DecisionApi, DecisionVO } from '@/api/project/decision'
import DecisionTable from './DecisionTable.vue'
import { usePeopleStore } from '@/store/modules/people'

const props = defineProps({
  basicsInfo: {
    type: Object as PropType<BasicsVO>
  }
})

const currentNode = ref(0)
const currentPage = ref('problem')
const { peopleList } = usePeopleStore()
const decisionList = ref<DecisionVO[]>([])
const loading = ref(false)

const onNodeChange = (node: number) => {
  currentNode.value = node
  onListDecision()
}

// const onTabChange = () => {}

/** 初始化数据 */
const initData = async () => {
  const initNode = await getIntDictOptions('project_decision_category')
  await nextTick()
  onNodeChange(initNode[0].value)
}
/** 查询决策列表 */
const onListDecision = async () => {
  loading.value = true
  try {
    const res = await DecisionApi.listDecision({
      basicsId: props.basicsInfo?.id,
      category: currentNode.value
    })
    decisionList.value = res
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  initData()
})
</script>

<style lang="scss" scoped>
.tr-container {
  padding: 5px;
  height: 98%;
  width: 13%;
  border-right: 1px solid var(--el-border-color-light);

  .tr-item {
    height: 60px;
    width: 100%;
    text-align: center;
    background-color: white;
    border-radius: 5px;
    cursor: pointer;
    border-bottom: 1px solid var(--el-border-color-light);
    .tr-item-title {
      height: 60px;
      line-height: 60px;
      font-size: 1rem;
      font-weight: bold;
      color: var(--regular-text-color);
    }
    .tr-item-content {
      height: 20px;
      line-height: 20px;
      font-size: 0.6vw;
      color: var(--placeholder-text-color);
    }

    &:hover,
    &.active {
      background-color: var(--el-color-primary-light-9) !important;

      & > .tr-item-title {
        color: var(--el-color-primary) !important;
      }
    }
  }
}

.header {
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 2px;
  border-bottom: 1px solid var(--el-border-color-light);
  :deep(.el-radio-button__inner) {
    border: 1px solid #f1f1f1 !important;
    // background-color: #ebeff3;
    font-size: 1rem;
    padding: 5px 5px;

    i {
      font-size: 1rem !important;

      .el-tooltip__trigger {
        font-size: 1rem !important;
      }
    }
  }
}
</style>
