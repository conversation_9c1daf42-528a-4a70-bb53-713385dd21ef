import { defineStore } from 'pinia'
import { getAllUser, UserVO } from '@/api/system/user'

export const usePeopleStore = defineStore('people', () => {
  const peopleList = ref<UserVO[]>([])

  const loading = ref(false)

  // 计算属性：常用筛选（如下拉选单用的人员列表）
  const peopleOptions = computed(() =>
    peopleList.value
      .filter((people) => people.status == 0)
      .map((p) => ({ label: p.nickname, value: p.id }))
  )

  // 动作：初始化/刷新人员列表
  const fetchPeopleList = async (force = false) => {
    // 已有数据且非强制刷新时，直接返回
    if (peopleList.value.length > 0 && !force) return
    loading.value = true
    try {
      const res = await getAllUser()
      peopleList.value = res
        .filter((p) => p.id !== 1 && ['0', '2'].includes(p.status))
        .map((p) => {
          return {
            ...p,
            nickname: p.nickname + (p.status == '2' ? '(离职)' : '')
          }
        })
    } catch (err) {
      console.error('获取人员列表失败', err)
    } finally {
      loading.value = false
    }
  }

  // 动作：更新人员信息（编辑/新增后同步状态）
  const updatePerson = (updatedPerson: UserVO) => {
    // 更新列表
    const index = peopleList.value.findIndex((p) => p.id === updatedPerson.id)
    if (index > -1) {
      peopleList.value[index] = updatedPerson
    } else {
      peopleList.value.push(updatedPerson) // 新增人员
    }
  }

  // 动作：删除人员（同步状态）
  const deletePerson = (personId: number) => {
    peopleList.value = peopleList.value.filter((p) => p.id !== personId)
  }

  // 动作：清空缓存（如登出时）
  const clearPeopleStore = () => {
    peopleList.value = []
  }

  return {
    peopleList,
    loading,
    peopleOptions,
    fetchPeopleList,
    updatePerson,
    deletePerson,
    clearPeopleStore
  }
})
